<template>
  <div class="message-item" :class="{ 'is-own': isOwn }">
    <div class="message-wrapper">
      <!-- {{ isOwn }} -->
      <!-- 他人消息：头像在左，消息在右 -->
      <template v-if="!isOwn">
        <div class="avatar-section">
          <ChatAvatar
            :size="36"
            :user-info="message"
            :clickable="true"
            custom-class="message-avatar"
            @click="handleAvatarClick"
          />
        </div>

        <div class="message-content">
          <div class="message-meta">
            <span class="nickname">{{ message.nickname }}</span>
            <span class="time">{{ formatTime(message.createdAt) }}</span>
          </div>

          <!-- 图片消息：不显示气泡 -->
          <div v-if="message.type === 'image'" class="image-content-no-bubble">
            <el-image
              :src="message.content"
              fit="cover"
              class="message-image"
              :preview-src-list="[message.content]"
              style="width: 100px; height: 100px; border-radius: 8px;"
            />
          </div>

          <!-- 其他消息：显示气泡 -->
          <div v-else class="message-bubble" :class="getBubbleClass()">
            <div class="bubble-content">
              <div v-if="message.type === 'text'" class="text-content">
                {{ message.content }}
              </div>

              <div v-else-if="message.type === 'file'" class="file-content">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ message.fileName || '文件' }}</span>
              </div>

              <div v-else-if="message.type === 'voice'" class="voice-content" @click="playVoice">
                <el-icon class="voice-icon" :class="{ 'playing': isPlaying }" color="#fff">
                  <Microphone />
                </el-icon>
                <span class="voice-text">语音 {{ isPlaying && remainingTime > 0 ? formatVoiceDuration(remainingTime) : formatVoiceDuration(getVoiceDuration()) }}</span>
                <audio ref="audioRef" :src="getVoiceUrl()" @ended="onVoiceEnded" preload="metadata"></audio>
              </div>


            </div>
          </div>
        </div>
      </template>

      <!-- 自己消息：头像在右，消息在左 -->
      <template v-else>
       

        <div class="message-content own-content">
          <div class="message-meta own-meta">
            <span class="time">{{ formatTime(message.createdAt) }}</span>
          </div>

          <!-- 图片消息：不显示气泡 -->
          <div v-if="message.type === 'image'" class="image-content-no-bubble own-image">
            <el-image
              :src="message.content"
              fit="cover"
              class="message-image"
              :preview-src-list="[message.content]"
              style="width: 100px; height: 100px; border-radius: 8px;"
            />
          </div>

          <!-- 其他消息：显示气泡 -->
          <div v-else class="message-bubble" :class="getBubbleClass()">
            <div class="bubble-content">
              <div v-if="message.type === 'text'" class="text-content">
                {{ message.content }}
              </div>

              <div v-else-if="message.type === 'file'" class="file-content">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ message.fileName || '文件' }}</span>
              </div>

              <div v-else-if="message.type === 'voice'" class="voice-content" @click="playVoice">
                <el-icon class="voice-icon" :class="{ 'playing': isPlaying }">
                  <Microphone />
                </el-icon>
                <span class="voice-text">语音 {{ isPlaying && remainingTime > 0 ? formatVoiceDuration(remainingTime) : formatVoiceDuration(getVoiceDuration()) }}</span>
                <audio ref="audioRef" :src="getVoiceUrl()" @ended="onVoiceEnded" preload="metadata"></audio>
              </div>


            </div>
            
          </div>
           
        </div>
        <div class="avatar-section" style="align-items: flex-end;">
          <ChatAvatar
            :size="36"
            :user-info="currentUserInfo"
            custom-class="message-avatar"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Document, Microphone } from '@element-plus/icons-vue'
import ChatAvatar from '@/components/ChatAvatar/index.vue'
import { useUserStore } from '@/pinia/modules/user.js'
import { processAvatarUrl, getDefaultAvatar } from '@/utils/avatarService.js'

defineOptions({
  name: 'MessageItem'
})

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  isOwn: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['avatar-click'])

// 获取用户store
const userStore = useUserStore()

// 计算当前用户的头像信息
const currentUserInfo = computed(() => {
  const userInfo = userStore.userInfo || {}
  return {
    id: userInfo.ID || userInfo.uuid || userInfo.id || '',
    nickname: userInfo.nickName || userInfo.userName || '我',
    avatar: processAvatarUrl(userInfo.headerImg) || getDefaultAvatar(),
    headerImg: userInfo.headerImg || ''
  }
})

// 格式化时间
const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取气泡样式类
const getBubbleClass = () => {
  return {
    'own-bubble': props.isOwn,
    'other-bubble': !props.isOwn,
    [`${props.message.type}-bubble`]: true
  }
}

// 头像点击事件
const handleAvatarClick = () => {
  console.log('点击头像:', props.message)
  emit('avatar-click', props.message)
}

// 语音播放相关
const audioRef = ref(null)
const isPlaying = ref(false)
const remainingTime = ref(0)
const playTimer = ref(null)

// 获取语音URL
const getVoiceUrl = () => {
  if (props.message.type === 'voice') {
    try {
      const voiceData = JSON.parse(props.message.content)
      return voiceData.url
    } catch {
      return props.message.content
    }
  }
  return ''
}

// 获取语音时长
const getVoiceDuration = () => {
  if (props.message.type === 'voice') {
    try {
      const voiceData = JSON.parse(props.message.content)
      return voiceData.duration || 0
    } catch {
      return 0
    }
  }
  return 0
}

// 格式化语音时长
const formatVoiceDuration = (seconds) => {
  if (seconds < 60) {
    return `${seconds}"`
  }
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}'${secs}"`
}

// 播放语音
const playVoice = () => {
  if (!audioRef.value) return

  if (isPlaying.value) {
    audioRef.value.pause()
    isPlaying.value = false
    clearInterval(playTimer.value)
    remainingTime.value = 0
  } else {
    // 停止其他正在播放的语音
    const allAudios = document.querySelectorAll('audio')
    allAudios.forEach(audio => {
      if (audio !== audioRef.value) {
        audio.pause()
      }
    })

    audioRef.value.currentTime = 0
    audioRef.value.play()
    isPlaying.value = true

    // 开始倒计时
    const duration = getVoiceDuration()
    remainingTime.value = duration
    playTimer.value = setInterval(() => {
      remainingTime.value--
      if (remainingTime.value <= 0) {
        clearInterval(playTimer.value)
        remainingTime.value = 0
      }
    }, 1000)
  }
}

// 语音播放结束
const onVoiceEnded = () => {
  isPlaying.value = false
  clearInterval(playTimer.value)
  remainingTime.value = 0
}
</script>

<style lang="scss" scoped>
.voice-icon{
  color: #fff !important;
}
.message-item {
  margin-bottom: 16px;
}

.message-wrapper {
  display: flex;
  gap: 10px;
  max-width: 100%;
  justify-content: flex-start; // 别人的消息默认左对齐
}

// 自己的消息：整体在右边显示
.message-item.is-own {
  .message-wrapper {
    flex-direction: row;
    justify-content: flex-end; // 整体右对齐
  }
}

.avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;

  .message-avatar {
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%; // 使用百分比而不是固定像素
  min-width: 0;

  &.own-content {
    align-items: flex-start; // 自己的消息左对齐
  }
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;

  .nickname {
    color: #f3f4f6;
    font-weight: 500;
  }

  .time {
    color: #9ca3af;
  }

  &.own-meta {
    justify-content: flex-start; // 自己的消息时间左对齐
    margin-bottom: 4px;
    margin-top: 0;
  }
}

.message-bubble {
  position: relative;
  display: inline-block; // 改为inline-block让宽度自适应内容
  max-width: 100%;
  min-width: fit-content; // 最小宽度适应内容
  word-wrap: break-word;
  border-radius: 12px;
  padding: 10px 14px;

  &.own-bubble {
    background: #22c55e;
    color: white;

    &::before {
      content: '';
      position: absolute;
      right: -6px;
      top: 12px;
      width: 0;
      height: 0;
      border-left: 6px solid #22c55e;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  &.other-bubble {
    background: #4b5563;
    color: #f3f4f6;
    border: 1px solid #6b7280;
    width: 0;

    &::after {
      content: '';
      position: absolute;
      left: -7px;
      top: 12px;
      width: 0;
      height: 0;
      border-right: 6px solid #4b5563;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  .bubble-content {
    .text-content {
      line-height: 1.4;
      white-space: pre-wrap; // 保持换行和空格
      word-break: break-word; // 长单词换行
    }

    .image-content {
      .message-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        cursor: pointer;
      }
    }

    .file-content {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px;

      .file-icon {
        font-size: 20px;
        color: #60a5fa;
      }

      .file-name {
        font-size: 14px;
      }
    }


  }

  .message-status {
    margin-top: 4px;
    text-align: right;
  }

  // 图片消息无气泡样式
  .image-content-no-bubble {
    .message-image {
      border-radius: 8px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }
    }

    &.own-image {
      display: flex;
      justify-content: flex-end;
    }
  }

    // 语音消息内容样式
    .voice-content {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

    /*   &:hover {
        background: rgba(255, 255, 255, 0.1);
      } */

      .voice-icon {
        font-size: 16px;
        color: #22c55e;
        transition: all 0.3s ease;

        &.playing {
          color: #ef4444;
          animation: voice-pulse 1s infinite;
        }
      }

      .voice-text {
        font-size: 14px;
        color: #ffffff;
        font-weight: 500;
        white-space: nowrap;
      }
    }
}

// 语音播放动画
@keyframes voice-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
