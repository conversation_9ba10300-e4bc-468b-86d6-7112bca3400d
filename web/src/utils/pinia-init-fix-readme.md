# Pinia Store 初始化顺序问题修复

## 问题描述

遇到错误：
```
Error: [🍍]: "getActiveP<PERSON>()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?
```

## 问题根源

在 `permission.js` 中过早调用了依赖 Pinia store 的 WebSocket 初始化函数，而此时 Pinia 还没有被初始化。

### 执行顺序问题：
1. `main.js` 第11行：导入 `permission.js`
2. `permission.js` 第153行：调用 `setupWebSocketForApp()` 
3. `setupWebSocketForApp()` 内部使用了 `useUserStore()` 和 `useWebSocketStore()`
4. `main.js` 第36行：`app.use(store)` 初始化 Pinia

## 修复方案

### 1. 移除 permission.js 中的 WebSocket 初始化

**修改前：**
```javascript
// permission.js
setupWebSocketForApp().catch(error => {
  console.error('WebSocket初始化失败:', error)
})
setupPageRefreshHandler()
```

**修改后：**
```javascript
// permission.js
// WebSocket初始化将在应用完全启动后进行
// 这里只设置页面刷新处理器（不依赖store）
setupPageRefreshHandler()
```

### 2. 在 main.js 中正确初始化 WebSocket

**修改后：**
```javascript
// main.js
app.use(run).use(ElementPlus).use(store).use(auth).use(router).mount('#app')

// 在应用完全初始化后再初始化WebSocket和页面刷新处理器
// 确保Pinia store已经可用
import { setupWebSocketForApp } from '@/utils/websocketInit.js'
import { setupCompletePageRefreshHandler } from '@/utils/pageRefreshHandler.js'

setupWebSocketForApp().catch(error => {
  console.error('WebSocket初始化失败:', error)
})

// 设置完整的页面刷新处理器
setupCompletePageRefreshHandler()
```

### 3. 拆分页面刷新处理器

将 `setupPageRefreshHandler` 拆分为两部分：

1. **`setupPageRefreshHandler()`** - 不依赖 store 的基础部分
2. **`setupCompletePageRefreshHandler()`** - 依赖 store 的完整功能

## 修改的文件

1. **`web/src/permission.js`** - 移除过早的 WebSocket 初始化
2. **`web/src/main.js`** - 在正确时机初始化 WebSocket
3. **`web/src/utils/pageRefreshHandler.js`** - 拆分处理器函数

## 执行顺序（修复后）

1. `main.js` 导入 `permission.js`
2. `permission.js` 调用 `setupPageRefreshHandler()`（基础版本，不依赖store）
3. `main.js` 执行 `app.use(store)` 初始化 Pinia
4. `main.js` 调用 `setupWebSocketForApp()`（此时store已可用）
5. `main.js` 调用 `setupCompletePageRefreshHandler()`（完整版本）

## 预期效果

- ✅ 不再出现 Pinia 初始化错误
- ✅ WebSocket 正常初始化
- ✅ 页面刷新处理器正常工作
- ✅ 保持原有功能不变

## 注意事项

- 确保所有依赖 Pinia store 的代码都在 `app.use(store)` 之后执行
- 页面刷新处理器的可见性变化监听增加了延迟，确保 store 已初始化
- 开发环境下的测试工具仍然可用
