/**
 * 页面刷新处理工具
 * 专门处理页面刷新后的 WebSocket 重连逻辑
 */

import { useUserStore } from '@/pinia/modules/user'
import { useWebSocketStore } from '@/pinia/modules/websocket'

/**
 * 检测是否为页面刷新
 */
export const isPageRefresh = () => {
  // 检查 performance.navigation.type (已废弃但仍可用)
  if (performance.navigation && performance.navigation.type === 1) {
    return true
  }
  
  // 检查 performance.getEntriesByType
  const navigationEntries = performance.getEntriesByType('navigation')
  if (navigationEntries.length > 0) {
    return navigationEntries[0].type === 'reload'
  }
  
  return false
}

/**
 * 页面刷新后的 WebSocket 恢复处理
 */
export const handleWebSocketAfterRefresh = async () => {
  if (!isPageRefresh()) {
    console.log('非页面刷新，跳过 WebSocket 恢复处理')
    return
  }
  
  console.log('检测到页面刷新，开始 WebSocket 恢复处理')
  
  // 等待一段时间确保所有 store 初始化完成
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()
    
    console.log('页面刷新检查 - 用户Token:', userStore.token ? '已加载' : '未加载')
    console.log('页面刷新检查 - WebSocket状态:', webSocketStore.connectionStatus)
    
    if (userStore.token) {
      if (!webSocketStore.isConnected && !webSocketStore.isConnecting) {
        console.log('页面刷新后发现用户已登录但 WebSocket 未连接，开始重连')
        
        // 启用持久连接
        webSocketStore.enablePersistentConnection()
        
        // 尝试重连
        try {
          await webSocketStore.initConnection(userStore.token, true)
          console.log('页面刷新后 WebSocket 重连成功')
        } catch (error) {
          console.log('页面刷新后 WebSocket 重连失败，持久连接机制将继续尝试:', error.message)
        }
      } else {
        console.log('页面刷新后 WebSocket 状态正常，无需处理')
      }
    } else {
      console.log('页面刷新后用户未登录，跳过 WebSocket 重连')
    }
  } catch (error) {
    console.error('页面刷新后 WebSocket 恢复处理失败:', error)
  }
}

/**
 * 设置页面刷新监听器（不依赖store的部分）
 */
export const setupPageRefreshHandler = () => {
  // 页面可见性变化监听器（延迟执行store相关逻辑）
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      // 延迟检查，避免频繁触发，并确保store已初始化
      setTimeout(() => {
        try {
          const userStore = useUserStore()
          const webSocketStore = useWebSocketStore()

          if (userStore.token && !webSocketStore.isConnected && !webSocketStore.isConnecting) {
            console.log('页面可见时发现 WebSocket 未连接，尝试重连')
            webSocketStore.forceReconnect().catch(error => {
              console.log('页面可见时重连失败:', error.message)
            })
          }
        } catch (error) {
          console.log('页面可见时检查WebSocket失败（可能store未初始化）:', error.message)
        }
      }, 2000) // 增加延迟确保store已初始化
    }
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)

  console.log('页面刷新处理器已设置（基础部分）')

  // 返回清理函数
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  }
}

/**
 * 设置完整的页面刷新处理器（包含store依赖的部分）
 * 应该在store初始化后调用
 */
export const setupCompletePageRefreshHandler = () => {
  // 页面加载完成后检查
  if (document.readyState === 'complete') {
    handleWebSocketAfterRefresh()
  } else {
    window.addEventListener('load', () => {
      handleWebSocketAfterRefresh()
    }, { once: true })
  }

  console.log('完整页面刷新处理器已设置')
}

/**
 * 强制检查并恢复 WebSocket 连接
 */
export const forceCheckWebSocketConnection = async () => {
  console.log('强制检查 WebSocket 连接状态')
  
  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()
    
    console.log('强制检查 - 用户登录状态:', !!userStore.token)
    console.log('强制检查 - WebSocket连接状态:', webSocketStore.connectionStatus)
    console.log('强制检查 - 是否连接中:', webSocketStore.isConnecting)
    
    if (userStore.token && !webSocketStore.isConnected && !webSocketStore.isConnecting) {
      console.log('强制检查发现需要重连，开始重连...')
      
      webSocketStore.enablePersistentConnection()
      
      try {
        await webSocketStore.forceReconnect()
        console.log('强制检查重连成功')
        return true
      } catch (error) {
        console.error('强制检查重连失败:', error)
        return false
      }
    } else {
      console.log('强制检查：WebSocket 状态正常或用户未登录')
      return true
    }
  } catch (error) {
    console.error('强制检查 WebSocket 连接失败:', error)
    return false
  }
}

// 在开发环境下将工具函数挂载到全局对象
if (process.env.NODE_ENV === 'development') {
  window.pageRefreshHandler = {
    isPageRefresh,
    handleWebSocketAfterRefresh,
    forceCheckWebSocketConnection,
    setupPageRefreshHandler,
    setupCompletePageRefreshHandler
  }

  console.log('🔧 页面刷新处理工具已加载到 window.pageRefreshHandler')
}
